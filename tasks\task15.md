I want to create second region manager - Region2Manager. To do so we will duplicate Region1Manager.tscn and script - this will be our base.
1. In region1manager add an export int parameter that controlls max items spawned in region (does not include rabbits - they have separate logic).
2. Duplicate Region1Manager.tscn and Region1Manager.cs and rename to Region2Manager.tscn and Region2Manager.cs. In Region2Manager.tscn change RegionId to 2. There is a chance to spawn Tree, Rock, Rock2, Berrybush and 2 Rabbits with interval 60s. Proportions are the same as in Region1Manager.
3. Do the same for Region3Manager and Region4Manager - duplicate Region2Manager.tscn and Region2Manager.cs. Logic should be the same (for now).
4. Make that region2-4 managers are active (can spawn etc) only if region 2/3/4 is unlocked! If not unlocked then they should be disabled untill this region 
4. Attach scenes for region 2,3,4 to world->RegionManagers - the same as region1manager (look how world.tscn looks).
6. In PlayerController when i click R then player use a tool. I also want to handle the same way right mouse click in player controller - so that right click uses selected item. 
